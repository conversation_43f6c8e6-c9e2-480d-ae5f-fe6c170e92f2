Metadata-Version: 2.4
Name: gemfinder
Version: 0.1.0
Summary: Solana gem finder - Track pump.fun swaps in real-time
Requires-Python: >=3.12
Requires-Dist: asyncpg>=0.29.0
Requires-Dist: base58>=2.1.0
Requires-Dist: construct>=2.10.0
Requires-Dist: fastapi>=0.104.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: jinja2>=3.1.0
Requires-Dist: psycopg2-binary>=2.9.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: solana>=0.34.0
Requires-Dist: solders>=0.21.0
Requires-Dist: sqlalchemy>=2.0.0
Requires-Dist: uvicorn[standard]>=0.24.0
Description-Content-Type: text/markdown

# 💎 Gemfinder - Solana Pump.fun Swap Tracker

A real-time tracking application for pump.fun swaps on Solana, built with Python, FastAPI, HTMX, and TimescaleDB.

## Features

- 🔥 **Real-time swap tracking** - Monitor pump.fun swaps as they happen
- 📊 **Live dashboard** - HTMX-powered interface with auto-refreshing data
- 🗄️ **Time-series storage** - TimescaleDB for efficient historical data storage
- 🔗 **QuickNode integration** - Streams data directly from Solana blockchain
- 🐳 **Docker ready** - Complete containerized setup
- 📱 **Responsive design** - Works on desktop and mobile

## Tech Stack

- **Backend**: Python 3.12, FastAPI, SQLAlchemy
- **Frontend**: HTMX, TailwindCSS
- **Database**: TimescaleDB (PostgreSQL extension)
- **Blockchain**: Solana via QuickNode Streams
- **Deployment**: Docker & Docker Compose

## Quick Start

### Prerequisites

- Docker and Docker Compose
- QuickNode account with Streams access
- UV package manager (optional, for local development)

### 1. Clone and Setup

```bash
git clone <your-repo>
cd gemfinder

# Copy environment template
cp .env.example .env
```

### 2. Configure QuickNode Stream

1. Create a QuickNode account and set up a Solana Mainnet endpoint
2. Go to Streams and create a new stream with these settings:
   - **Chain**: Solana Mainnet
   - **Stream Start**: Latest Block
   - **Filter**: Use the provided filter code (see below)
   - **Destination**: Webhook pointing to your app

### 3. QuickNode Stream Filter

Use this filter code in your QuickNode Stream:

```javascript
const BASE58_ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
const PUMP_FUN_PROGRAM_ID = 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA';

function main(stream) {
    try {
        const data = stream.data[0];
        if (!data?.transactions?.length) {
            return { error: 'Invalid or missing stream' };
        }

        const matchedTransactions = data.transactions
            .filter(tx => {
                // Filter for pump.fun program transactions
                if (tx.meta?.err !== null) return false;

                const programIds = new Set(tx.transaction.message.instructions.map(ix => ix.programId));
                return programIds.has(PUMP_FUN_PROGRAM_ID);
            })
            .map(tx => ({
                signature: tx.transaction.signatures[0],
                blockTime: data.blockTime,
                blockHeight: data.blockHeight,
                accounts: {
                    mint: tx.transaction.message.instructions
                        .find(ix => ix.programId === PUMP_FUN_PROGRAM_ID)?.accounts[0] || null
                },
                programId: PUMP_FUN_PROGRAM_ID
            }))
            .filter(tx => tx.accounts.mint);

        if (matchedTransactions.length === 0) {
            return null;
        }

        return { matchedTransactions };
    } catch (error) {
        console.error('Error in main function:', error);
        return { error: error.message };
    }
}
```

### 4. Update Environment Variables

Edit `.env` file:

```env
DATABASE_URL=****************************************************/gemfinder
QUICKNODE_WEBHOOK_SECRET=your-actual-webhook-secret
PUMP_FUN_PROGRAM_ID=pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA
```

### 5. Start the Application

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app
```

The application will be available at:
- **Dashboard**: http://localhost:8000
- **API**: http://localhost:8000/api/swaps
- **Health Check**: http://localhost:8000/health

## Development

### Local Development Setup

```bash
# Install dependencies
uv sync

# Start TimescaleDB only
docker-compose up timescaledb -d

# Run app locally
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Project Structure

```
gemfinder/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application
│   ├── models.py            # Database models
│   ├── database.py          # Database connection
│   ├── stream_listener.py   # QuickNode webhook handler
│   └── templates/
│       └── index.html       # HTMX dashboard
├── static/
│   └── style.css           # Custom styles
├── Dockerfile              # App container
├── docker-compose.yml      # Multi-service setup
├── init.sql               # Database initialization
└── pyproject.toml         # Python dependencies
```

## API Endpoints

- `GET /` - Dashboard (HTML)
- `POST /webhook/quicknode` - QuickNode webhook receiver
- `GET /api/swaps` - Get swaps with pagination
- `GET /api/swaps/recent` - Recent swaps (HTMX fragment)
- `GET /api/stats` - Application statistics
- `GET /health` - Health check

## Database Schema

The `swaps` table stores:
- Transaction signatures and timestamps
- Token mint addresses
- Swap amounts and prices
- User wallet addresses
- Raw transaction data (JSONB)

TimescaleDB provides automatic partitioning and compression for time-series data.

## Monitoring

- **Health endpoint**: `/health`
- **Database status**: Included in health check
- **Logs**: `docker-compose logs -f`
- **Metrics**: Built-in statistics at `/api/stats`

## Troubleshooting

### Common Issues

1. **Database connection failed**
   ```bash
   # Check if TimescaleDB is running
   docker-compose ps timescaledb

   # View database logs
   docker-compose logs timescaledb
   ```

2. **No swaps appearing**
   - Verify QuickNode stream is active
   - Check webhook URL is accessible
   - Review application logs for errors

3. **HTMX not updating**
   - Check browser console for errors
   - Verify API endpoints are responding
   - Ensure proper CORS configuration

### Logs and Debugging

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f app
docker-compose logs -f timescaledb

# Access database directly
docker-compose exec timescaledb psql -U gemfinder -d gemfinder
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.