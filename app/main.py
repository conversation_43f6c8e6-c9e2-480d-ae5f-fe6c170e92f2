"""Main FastAPI application for Gemfinder."""

import logging
from datetime import datetime, timedelta, timezone
from typing import List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Depends, HTTPException, Request, Response
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from app.database import get_db, init_db, test_connection
from app.models import Swap, SwapResponse, TokenStats, Token
from app.transaction_decoder import transaction_log_listener

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting Gemfinder application...")

    # Test database connection
    if not test_connection():
        logger.error("Failed to connect to database")
        raise Exception("Database connection failed")

    # Initialize database
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

    # Start Solana WebSocket listener
    try:
        transaction_log_listener.start()
        logger.info("Started Solana WebSocket listener")
    except Exception as e:
        logger.error(f"Failed to start WebSocket listener: {e}")
        # Don't raise - app can still work without live data

    yield

    # Shutdown
    logger.info("Shutting down Gemfinder application...")

    # Stop WebSocket listener
    try:
        transaction_log_listener.stop()
        logger.info("Stopped Solana WebSocket listener")
    except Exception as e:
        logger.error(f"Error stopping WebSocket listener: {e}")


# Create FastAPI app
app = FastAPI(
    title="Gemfinder",
    description="Solana pump.fun swap tracker",
    version="0.1.0",
    lifespan=lifespan
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="app/templates")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc),
        "database": test_connection(),
        "websocket_listener": transaction_log_listener.running
    }


@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    """Main dashboard page."""
    try:
        # Get recent swaps with token information (last 100)
        recent_swaps_query = db.query(
            Swap.id,
            Swap.timestamp,
            Swap.signature,
            Swap.mint_address,
            Swap.token_amount,
            Swap.sol_amount,
            Swap.price_per_token,
            Swap.swap_type,
            Swap.user_wallet,
            Token.name.label('token_name'),
            Token.symbol.label('token_symbol'),
            Token.decimals.label('token_decimals')
        ).join(Token, Swap.mint_address == Token.mint_address)\
         .order_by(desc(Swap.timestamp))\
         .limit(100).all()

        # Convert to display format
        recent_swaps = []
        for swap in recent_swaps_query:
            sol_display = swap.sol_amount / 1_000_000_000  # Convert lamports to SOL (9 decimals)
            token_display = swap.token_amount / (10 ** swap.token_decimals)  # Convert to token units

            recent_swaps.append({
                'id': swap.id,
                'timestamp': swap.timestamp,
                'signature': swap.signature,
                'mint_address': swap.mint_address,
                'token_amount': swap.token_amount,
                'sol_amount': swap.sol_amount,
                'price_per_token': swap.price_per_token,
                'swap_type': swap.swap_type,
                'user_wallet': swap.user_wallet,
                'token_name': swap.token_name,
                'token_symbol': swap.token_symbol,
                'token_decimals': swap.token_decimals,
                'sol_display': sol_display,
                'token_display': token_display
            })

        # Get total counts
        total_swaps = db.query(func.count(Swap.id)).scalar()
        unique_tokens = db.query(func.count(func.distinct(Swap.mint_address))).scalar()

        # Get swaps from last 24 hours
        yesterday = datetime.now(timezone.utc) - timedelta(hours=24)
        recent_count = db.query(func.count(Swap.id)).filter(Swap.timestamp >= yesterday).scalar()

        return templates.TemplateResponse("index.html", {
            "request": request,
            "recent_swaps": recent_swaps,
            "total_swaps": total_swaps or 0,
            "unique_tokens": unique_tokens or 0,
            "recent_count": recent_count or 0
        })

    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        raise HTTPException(status_code=500, detail="Failed to load dashboard")


@app.get("/api/swaps", response_model=List[SwapResponse])
async def get_swaps(
    limit: int = 50,
    offset: int = 0,
    mint_address: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get swaps with optional filtering."""
    try:
        query = db.query(Swap)

        if mint_address:
            query = query.filter(Swap.mint_address == mint_address)

        swaps = query.order_by(desc(Swap.timestamp)).offset(offset).limit(limit).all()

        # Add seconds_ago calculation
        now = datetime.now(timezone.utc)
        swap_responses = []
        for swap in swaps:
            swap_dict = swap.__dict__.copy()
            swap_dict['seconds_ago'] = (now - swap.timestamp).total_seconds()
            swap_responses.append(SwapResponse(**swap_dict))

        return swap_responses

    except Exception as e:
        logger.error(f"Error fetching swaps: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch swaps")


@app.get("/api/swaps/recent")
async def get_recent_swaps_htmx(
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """Get recent swaps for HTMX updates."""
    try:
        # Get recent swaps with token information
        swaps_query = db.query(
            Swap.id,
            Swap.timestamp,
            Swap.signature,
            Swap.mint_address,
            Swap.token_amount,
            Swap.sol_amount,
            Swap.price_per_token,
            Swap.swap_type,
            Swap.user_wallet,
            Token.name.label('token_name'),
            Token.symbol.label('token_symbol'),
            Token.decimals.label('token_decimals')
        ).join(Token, Swap.mint_address == Token.mint_address)\
         .order_by(desc(Swap.timestamp))\
         .limit(limit).all()

        # Return HTML fragment for HTMX
        html_content = ""
        for swap in swaps_query:
            seconds_ago = (datetime.now(timezone.utc) - swap.timestamp).total_seconds()
            time_str = f"{int(seconds_ago)}s ago" if seconds_ago < 60 else f"{int(seconds_ago/60)}m ago"

            # Format display values
            sol_display = swap.sol_amount / 1_000_000_000  # Convert lamports to SOL
            token_display = swap.token_amount / (10 ** swap.token_decimals)  # Convert to token units

            # Display token name/symbol or mint address
            token_display_name = swap.token_symbol or swap.token_name or f"{swap.mint_address[:8]}...{swap.mint_address[-8:]}"

            # Color coding for buy/sell
            type_color = "text-green-400" if swap.swap_type == "buy" else "text-red-400"
            type_icon = "🟢" if swap.swap_type == "buy" else "🔴"

            html_content += f"""
            <tr class="swap-row hover:bg-gray-700 transition-colors">
                <td class="px-4 py-2 text-sm font-mono">
                    <span class="text-purple-400">{token_display_name}</span>
                </td>
                <td class="px-4 py-2 text-sm">
                    <span class="{type_color}">{type_icon} {swap.swap_type.title()}</span>
                </td>
                <td class="px-4 py-2 text-sm">
                    <span class="text-yellow-400">{sol_display:.9f} SOL</span>
                </td>
                <td class="px-4 py-2 text-sm">
                    <span class="text-blue-400">{token_display:.6f}</span>
                </td>
                <td class="px-4 py-2 text-sm text-gray-400">
                    <span class="time-ago" data-timestamp="{swap.timestamp.isoformat()}">{time_str}</span>
                </td>
                <td class="px-4 py-2 text-sm">
                    <a href="https://solscan.io/tx/{swap.signature}" target="_blank"
                       class="text-blue-400 hover:text-blue-300 transition-colors">View →</a>
                </td>
            </tr>
            """

        return Response(content=html_content, media_type="text/html")

    except Exception as e:
        logger.error(f"Error fetching recent swaps: {e}")
        return Response(content="<tr><td colspan='6' class='px-4 py-8 text-center text-gray-400'>Error loading swaps</td></tr>", media_type="text/html")


@app.get("/api/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get application statistics."""
    try:
        total_swaps = db.query(func.count(Swap.id)).scalar()
        unique_tokens = db.query(func.count(func.distinct(Swap.mint_address))).scalar()

        # Get swaps from different time periods
        now = datetime.now(timezone.utc)
        hour_ago = now - timedelta(hours=1)
        day_ago = now - timedelta(days=1)

        swaps_last_hour = db.query(func.count(Swap.id)).filter(Swap.timestamp >= hour_ago).scalar()
        swaps_last_day = db.query(func.count(Swap.id)).filter(Swap.timestamp >= day_ago).scalar()

        return {
            "total_swaps": total_swaps or 0,
            "unique_tokens": unique_tokens or 0,
            "swaps_last_hour": swaps_last_hour or 0,
            "swaps_last_day": swaps_last_day or 0,
            "timestamp": now
        }

    except Exception as e:
        logger.error(f"Error fetching stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch statistics")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
