"""Database models for the gemfinder application."""

from sqlalchemy import <PERSON>umn, Integer, String, BigInteger, DECIMAL, Index
from sqlalchemy.dialects.postgresql import TIMESTAMP, JSONB
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from app.database import Base


class Swap(Base):
    """Model for storing pump.fun swap transactions."""

    __tablename__ = "swaps"

    id = Column(Integer, primary_key=True)
    timestamp = Column(TIMESTAMP(timezone=True), primary_key=True, default=func.now(), nullable=False, index=True)
    block_time = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    block_height = Column(BigInteger, nullable=True)
    signature = Column(String, nullable=False, index=True)  # Changed to String (TEXT)
    program_id = Column(String, nullable=False, index=True)  # Changed to String (TEXT)
    mint_address = Column(String, nullable=False, index=True)  # Changed to String (TEXT)
    token_amount = Column(BigInteger, nullable=True)
    sol_amount = Column(BigInteger, nullable=True)
    price_per_token = Column(DECIMAL(20, 10), nullable=True)
    swap_type = Column(String, nullable=True, index=True)  # Changed to String (TEXT)
    user_wallet = Column(String, nullable=True, index=True)  # Changed to String (TEXT)
    raw_data = Column(JSONB, nullable=True)
    created_at = Column(TIMESTAMP(timezone=True), default=func.now(), nullable=False)

    # Add composite indexes for better query performance
    __table_args__ = (
        Index('idx_swaps_mint_timestamp', 'mint_address', 'timestamp'),
        Index('idx_swaps_user_timestamp', 'user_wallet', 'timestamp'),
        Index('idx_swaps_type_timestamp', 'swap_type', 'timestamp'),
        Index('idx_swaps_signature_timestamp', 'signature', 'timestamp', unique=True),
    )


# Pydantic models for API serialization
class SwapBase(BaseModel):
    """Base swap model for API responses."""

    signature: str = Field(..., description="Transaction signature")
    block_time: datetime = Field(..., description="Block timestamp")
    block_height: Optional[int] = Field(None, description="Block height")
    program_id: str = Field(..., description="Program ID")
    mint_address: str = Field(..., description="Token mint address")
    token_amount: Optional[int] = Field(None, description="Token amount in smallest unit")
    sol_amount: Optional[int] = Field(None, description="SOL amount in lamports")
    price_per_token: Optional[float] = Field(None, description="Price per token in SOL")
    swap_type: Optional[str] = Field(None, description="Swap type: buy or sell")
    user_wallet: Optional[str] = Field(None, description="User wallet address")

    class Config:
        from_attributes = True


class SwapCreate(BaseModel):
    """Model for creating new swap records."""

    signature: str
    block_time: datetime
    block_height: Optional[int] = None
    program_id: str
    mint_address: str
    token_amount: Optional[int] = None
    sol_amount: Optional[int] = None
    price_per_token: Optional[float] = None
    swap_type: Optional[str] = None
    user_wallet: Optional[str] = None
    raw_data: Optional[Dict[str, Any]] = None


class SwapResponse(SwapBase):
    """Extended swap model with additional fields for API responses."""

    id: int
    timestamp: datetime
    created_at: datetime
    seconds_ago: Optional[float] = Field(None, description="Seconds since swap occurred")


class TokenStats(BaseModel):
    """Model for token statistics."""

    mint_address: str
    total_swaps: int
    buy_count: int
    sell_count: int
    total_sol_bought: Optional[float] = None
    total_sol_sold: Optional[float] = None
    avg_price: Optional[float] = None
    max_price: Optional[float] = None
    min_price: Optional[float] = None
    last_swap_time: Optional[datetime] = None

    class Config:
        from_attributes = True
