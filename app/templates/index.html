<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemfinder - Solana Pump.fun Tracker</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/static/style.css">
</head>

<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <header class="bg-gray-800 shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <h1 class="text-3xl font-bold text-purple-400">💎 Gemfinder</h1>
                <p class="text-gray-300">Real-time Pump.fun Swap Tracker</p>
            </div>
        </div>
    </header>

    <!-- Stats Dashboard -->
    <section class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Total Swaps</h3>
                <p class="text-3xl font-bold text-green-400" id="total-swaps">{{ total_swaps }}</p>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Unique Tokens</h3>
                <p class="text-3xl font-bold text-blue-400" id="unique-tokens">{{ unique_tokens }}</p>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Last 24h</h3>
                <p class="text-3xl font-bold text-purple-400" id="recent-count">{{ recent_count }}</p>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Status</h3>
                <p class="text-3xl font-bold text-green-400">🟢 Live</p>
            </div>
        </div>

        <!-- Auto-refresh stats every 30 seconds -->
        <div hx-get="/api/stats" hx-trigger="every 30s" hx-target="#stats-container" hx-swap="none"
            style="display: none;"></div>
    </section>

    <!-- Recent Swaps Table -->
    <section class="container mx-auto px-4 pb-8">
        <div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-700">
                <h2 class="text-xl font-semibold text-white flex items-center">
                    <span class="mr-2">🔥</span>
                    Recent Swaps
                    <span class="ml-auto text-sm text-gray-400">Auto-refreshing every 5s</span>
                </h2>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Token</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Type</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">SOL Amount</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Token Amount</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Time</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Transaction</th>
                        </tr>
                    </thead>
                    <tbody id="swaps-table" class="divide-y divide-gray-700" hx-get="/api/swaps/recent"
                        hx-trigger="every 5s" hx-swap="innerHTML">
                        {% for swap in recent_swaps %}
                        <tr class="swap-row hover:bg-gray-700 transition-colors">
                            <td class="px-4 py-2 text-sm font-mono">
                                <span class="text-purple-400">
                                    {% if swap.token_symbol %}
                                    {{ swap.token_symbol }}
                                    {% elif swap.token_name %}
                                    {{ swap.token_name }}
                                    {% else %}
                                    {{ swap.mint_address[:8] }}...{{ swap.mint_address[-8:] }}
                                    {% endif %}
                                </span>
                            </td>
                            <td class="px-4 py-2 text-sm">
                                {% if swap.swap_type == 'buy' %}
                                <span class="text-green-400">🟢 Buy</span>
                                {% elif swap.swap_type == 'sell' %}
                                <span class="text-red-400">🔴 Sell</span>
                                {% else %}
                                <span class="text-gray-400">❓ Unknown</span>
                                {% endif %}
                            </td>
                            <td class="px-4 py-2 text-sm">
                                <span class="text-yellow-400">{{ "%.9f"|format(swap.sol_display) }} SOL</span>
                            </td>
                            <td class="px-4 py-2 text-sm">
                                <span class="text-blue-400">{{ "%.6f"|format(swap.token_display) }}</span>
                            </td>
                            <td class="px-4 py-2 text-sm text-gray-400">
                                <span class="time-ago" data-timestamp="{{ swap.timestamp.isoformat() }}">
                                    Just now
                                </span>
                            </td>
                            <td class="px-4 py-2 text-sm">
                                <a href="https://solscan.io/tx/{{ swap.signature }}" target="_blank"
                                    class="text-blue-400 hover:text-blue-300 transition-colors">
                                    View →
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if not recent_swaps %}
            <div class="px-6 py-8 text-center text-gray-400">
                <p class="text-lg">No swaps detected yet...</p>
                <p class="text-sm mt-2">Waiting for pump.fun transactions to stream in.</p>
            </div>
            {% endif %}
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-gray-700 mt-12">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <p class="text-gray-400 text-sm">
                    Powered by QuickNode Streams & TimescaleDB
                </p>
                <div class="flex space-x-4 text-sm text-gray-400">
                    <a href="/api/swaps" class="hover:text-white transition-colors">API</a>
                    <a href="/health" class="hover:text-white transition-colors">Health</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Update time ago displays
        function updateTimeAgo() {
            document.querySelectorAll('.time-ago').forEach(element => {
                const timestamp = element.getAttribute('data-timestamp');
                if (timestamp) {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const diffInSeconds = Math.floor((now - date) / 1000);

                    let timeAgo;
                    if (diffInSeconds < 60) {
                        timeAgo = `${diffInSeconds}s ago`;
                    } else if (diffInSeconds < 3600) {
                        timeAgo = `${Math.floor(diffInSeconds / 60)}m ago`;
                    } else {
                        timeAgo = `${Math.floor(diffInSeconds / 3600)}h ago`;
                    }

                    element.textContent = timeAgo;
                }
            });
        }

        // Update time displays every second
        setInterval(updateTimeAgo, 1000);

        // Initial update
        updateTimeAgo();

        // Handle stats updates
        document.body.addEventListener('htmx:afterRequest', function (event) {
            if (event.detail.pathInfo.requestPath === '/api/stats') {
                const response = JSON.parse(event.detail.xhr.responseText);
                document.getElementById('total-swaps').textContent = response.total_swaps;
                document.getElementById('unique-tokens').textContent = response.unique_tokens;
                document.getElementById('recent-count').textContent = response.swaps_last_day;
            }
        });

        // Add visual feedback for new swaps
        document.body.addEventListener('htmx:afterSwap', function (event) {
            if (event.detail.target.id === 'swaps-table') {
                // Highlight new rows briefly
                const rows = event.detail.target.querySelectorAll('.swap-row');
                rows.forEach((row, index) => {
                    if (index < 3) { // Highlight first 3 rows as "new"
                        row.classList.add('bg-purple-900', 'bg-opacity-30');
                        setTimeout(() => {
                            row.classList.remove('bg-purple-900', 'bg-opacity-30');
                        }, 2000);
                    }
                });

                // Update time ago for new content
                updateTimeAgo();
            }
        });
    </script>
</body>

</html>