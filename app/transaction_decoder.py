"""
Advanced transaction decoder for pump.fun using logs subscription.
This approach listens to transaction logs which contain the actual trade events.
Uses Solana SDK's built-in websocket functionality.
"""

import os
import json
import asyncio
import threading
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import base64
import struct

from solana.rpc.websocket_api import connect
from solders.pubkey import Pubkey
from solders.rpc.config import RpcTransactionLogsFilterMentions
from solders.rpc.responses import SubscriptionResult

from app.database import SessionLocal
from app.models import SwapCreate, Swap, Token, TokenCreate

logger = logging.getLogger(__name__)

# Configuration
PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
SOLANA_WS_URL = os.getenv("SOLANA_WS_URL", "wss://api.mainnet-beta.solana.com")
SOLANA_RPC_URL = os.getenv("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")

# Event discriminators from IDL (correct discriminators)
TRADE_EVENT_DISCRIMINATOR = bytes([189, 219, 127, 211, 78, 230, 97, 238])  # TradeEvent from pump_fun IDL


class PumpFunEventDecoder:
    """Decoder for pump.fun events from transaction logs."""

    def __init__(self):
        self.program_id = PUMP_FUN_PROGRAM_ID

    def decode_log_data(self, log_data: str) -> Optional[Dict[str, Any]]:
        """Decode base64 log data from transaction logs."""
        try:
            # Decode base64 log data
            raw_data = base64.b64decode(log_data)

            if len(raw_data) < 8:
                return None

            # Check for event discriminator
            discriminator = raw_data[:8]

            if discriminator == TRADE_EVENT_DISCRIMINATOR:
                data = self._decode_trade_event(raw_data[8:])
            else:
                return None

            # Convert pubkey bytes to Pubkey objects
            if data and "error" not in data:
                data["mint"] = str(Pubkey.from_bytes(bytes.fromhex(data["mint"])))
                data["user"] = str(Pubkey.from_bytes(bytes.fromhex(data["user"])))

            return data

        except Exception as e:
            logger.error(f"Error decoding log data: {e}")
            return None

    def _decode_trade_event(self, data: bytes) -> Dict[str, Any]:
        """Decode TradeEvent from log data - only extract essential swap data."""
        try:
            # TradeEvent structure from IDL (in order):
            # mint: pubkey, sol_amount: u64, token_amount: u64, is_buy: bool,
            # user: pubkey, timestamp: i64, ...rest we don't need

            # We only need the first 81 bytes: mint(32) + sol_amount(8) + token_amount(8) + is_buy(1) + user(32) + timestamp(8)
            min_required_size = 32 + 8 + 8 + 1 + 32 + 8  # 89 bytes
            if len(data) < min_required_size:
                logger.warning(f"Trade event data too short: {len(data)} bytes, expected at least {min_required_size}")
                return {"error": "insufficient_data"}

            # Unpack only the essential data (little-endian)
            offset = 0

            # mint: pubkey
            mint = data[offset:offset+32].hex()
            offset += 32

            # sol_amount: u64
            sol_amount = struct.unpack('<Q', data[offset:offset+8])[0]
            offset += 8

            # token_amount: u64
            token_amount = struct.unpack('<Q', data[offset:offset+8])[0]
            offset += 8

            # is_buy: bool
            is_buy = struct.unpack('<?', data[offset:offset+1])[0]
            offset += 1

            # user: pubkey
            user = data[offset:offset+32].hex()
            offset += 32

            # timestamp: i64
            timestamp = struct.unpack('<q', data[offset:offset+8])[0]
            offset += 8

            # Calculate price per token
            price_per_token = sol_amount / token_amount if token_amount > 0 else 0

            return {
                "mint": mint,
                "sol_amount": sol_amount,
                "token_amount": token_amount,
                "is_buy": is_buy,
                "user": user,
                "timestamp": timestamp,
                "price_per_token": price_per_token,
            }

        except Exception as e:
            logger.error(f"Error decoding trade event: {e}")
            return {"error": str(e)}




class TransactionLogListener:
    """WebSocket listener for Solana transaction logs using Solana SDK."""

    def __init__(self):
        self.ws_url = SOLANA_WS_URL
        self.rpc_url = SOLANA_RPC_URL
        self.program_id = PUMP_FUN_PROGRAM_ID
        self.decoder = PumpFunEventDecoder()
        self.running = False
        self.thread = None
        self._rpc_client = None
        self._subscription_id = None

    def start(self):
        """Start the transaction log listener in a separate thread."""
        if self.running:
            logger.warning("Transaction log listener is already running")
            return

        self.running = True
        self.thread = threading.Thread(target=self._run_listener, daemon=True)
        self.thread.start()
        logger.info("Started transaction log listener thread")

    def stop(self):
        """Stop the transaction log listener."""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("Stopped transaction log listener")

    def _run_listener(self):
        """Run the listener (runs in separate thread)."""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._listen())
        except Exception as e:
            logger.error(f"Error in transaction log listener thread: {e}")
        finally:
            try:
                loop.close()
            except:
                pass

    async def _listen(self):
        """Async transaction log listener using Solana SDK."""
        while self.running:
            try:
                logger.info(f"Connecting to Solana WebSocket: {self.ws_url}")

                # Initialize RPC client for any supplementary calls
                if not self._rpc_client:
                    from solana.rpc.api import Client
                    self._rpc_client = Client(self.rpc_url)

                async with connect(self.ws_url) as websocket:
                    logger.info("Connected to Solana WebSocket")

                    try:
                        # Subscribe to logs mentioning our program
                        logger.info(f"Attempting to subscribe to program: {self.program_id}")

                        # Validate the program ID format
                        try:
                            program_pubkey = Pubkey.from_string(self.program_id)
                            logger.info(f"Program pubkey created successfully: {program_pubkey}")
                        except Exception as pubkey_error:
                            logger.error(f"Invalid program ID format: {pubkey_error}")
                            continue

                        subscription_id = await websocket.logs_subscribe(
                            filter_=RpcTransactionLogsFilterMentions(program_pubkey),
                            commitment="confirmed"
                        )
                        self._subscription_id = subscription_id
                        logger.info(f"Subscribed to pump.fun program logs with ID: {subscription_id}")

                    except Exception as e:
                        logger.error(f"Failed to subscribe to logs: {e}")
                        import traceback
                        logger.error(f"Subscription error traceback: {traceback.format_exc()}")
                        continue

                    # Listen for messages
                    async for msg_list in websocket:
                        if not self.running:
                            break

                        logger.info(f"Received {len(msg_list)} message(s)")

                        for msg in msg_list:
                            try:
                                logger.debug(f"Processing message type: {type(msg)}")
                                await self._process_solana_message(msg)
                            except Exception as e:
                                logger.error(f"Error processing message: {e}")
                                logger.error(f"Message content: {msg}")

            except Exception as e:
                logger.error(f"WebSocket connection error: {e}")
                if self.running:
                    logger.info("Reconnecting in 5 seconds...")
                    await asyncio.sleep(5)

    async def _process_solana_message(self, msg):
        """Process incoming Solana SDK message."""
        try:
            logger.debug(f"Message attributes: {dir(msg)}")

            # Handle subscription confirmation
            if isinstance(msg, SubscriptionResult):
                logger.info(f"Subscription confirmed with ID: {msg.result}")
                return

            # Handle logs notification
            if hasattr(msg, 'result') and hasattr(msg.result, 'value'):
                logger.info(f"Processing logs notification for signature: {getattr(msg.result.value, 'signature', 'unknown')}")
                await self._process_logs_notification_sdk(msg.result.value)
            else:
                logger.warning(f"Unhandled message type: {type(msg)}, content: {msg}")

        except Exception as e:
            logger.error(f"Error processing Solana message: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    async def _process_logs_notification_sdk(self, log_result):
        """Process logs notification from Solana SDK and extract swap events."""
        try:
            signature = getattr(log_result, 'signature', None)
            logs = getattr(log_result, 'logs', [])

            logger.info(f"Processing logs for signature: {signature}")
            logger.info(f"Number of logs: {len(logs)}")

            if not signature or not logs:
                logger.warning("Missing signature or logs")
                return

            # Log all the logs to see what we're getting
            for i, log in enumerate(logs):
                logger.debug(f"Log {i}: {log}")

            # Look for program data logs (these contain the event data)
            program_data_found = False
            for log in logs:
                if log.startswith("Program data: "):
                    program_data_found = True
                    logger.info(f"Found program data log: {log[:100]}...")

                    # Extract base64 data
                    log_data = log.replace("Program data: ", "")

                    # Decode the event
                    event_data = self.decoder.decode_log_data(log_data)

                    if event_data and "error" not in event_data:
                        logger.info(f"Successfully decoded event: {event_data}")
                        # Create swap record
                        await self._create_swap_from_event(str(signature), event_data)
                    else:
                        logger.warning(f"Failed to decode event data: {event_data}")

            if not program_data_found:
                logger.info("No 'Program data:' logs found in this transaction")

        except Exception as e:
            logger.error(f"Error processing logs notification: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    # Keep the old method for backward compatibility
    async def _process_message(self, message: str):
        """Process incoming WebSocket message (legacy method)."""
        try:
            data = json.loads(message)

            if "result" in data and "id" in data:
                logger.info(f"Log subscription confirmed: {data}")
                return

            if "method" in data and data["method"] == "logsNotification":
                await self._process_logs_notification(data)

        except Exception as e:
            logger.error(f"Error processing log message: {e}")

    async def _process_logs_notification(self, notification: Dict[str, Any]):
        """Process logs notification and extract swap events (legacy method)."""
        try:
            params = notification.get("params", {})
            result = params.get("result", {})
            value = result.get("value", {})

            signature = value.get("signature")
            logs = value.get("logs", [])

            if not signature or not logs:
                return

            # Look for program data logs (these contain the event data)
            for log in logs:
                if log.startswith("Program data: "):
                    # Extract base64 data
                    log_data = log.replace("Program data: ", "")

                    # Decode the event
                    event_data = self.decoder.decode_log_data(log_data)

                    if event_data and "error" not in event_data:
                        # Create swap record
                        await self._create_swap_from_event(signature, event_data)

        except Exception as e:
            logger.error(f"Error processing logs notification: {e}")

    async def _create_swap_from_event(self, signature: str, event_data: Dict[str, Any]):
        """Create swap record from decoded event data."""
        try:
            # Extract data from TradeEvent
            mint_address = event_data["mint"]
            token_amount = event_data["token_amount"]
            sol_amount = event_data["sol_amount"]
            swap_type = "buy" if event_data["is_buy"] else "sell"
            user_wallet = event_data["user"]
            price_per_token = event_data["price_per_token"]

            swap_data = SwapCreate(
                signature=signature,
                mint_address=mint_address,
                token_amount=token_amount,
                sol_amount=sol_amount,
                price_per_token=price_per_token,
                swap_type=swap_type,
                user_wallet=user_wallet
            )

            # Store in database
            self._store_swap(swap_data)

        except Exception as e:
            logger.error(f"Error creating swap from event: {e}")

    def _store_swap(self, swap_data: SwapCreate):
        """Store swap data in database."""
        try:
            db = SessionLocal()
            try:
                # Check if swap already exists
                existing = db.query(Swap).filter(
                    Swap.signature == swap_data.signature
                ).first()

                if existing:
                    logger.debug(f"Swap {swap_data.signature} already exists")
                    return

                # Ensure token exists in database
                token = db.query(Token).filter(
                    Token.mint_address == swap_data.mint_address
                ).first()

                if not token:
                    # Create new token with default values
                    token = Token(
                        mint_address=swap_data.mint_address,
                        name=None,  # Will be populated later if needed
                        symbol=None,  # Will be populated later if needed
                        decimals=6  # Default for pump.fun tokens
                    )
                    db.add(token)
                    db.flush()  # Ensure token is created before swap

                # Create swap record
                db_swap = Swap(**swap_data.model_dump())
                db.add(db_swap)
                db.commit()

                logger.info(f"Stored {swap_data.swap_type} swap: {swap_data.signature} "
                           f"({swap_data.token_amount} tokens for {swap_data.sol_amount} lamports)")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error storing swap: {e}")


# Global listener instance
transaction_log_listener = TransactionLogListener()

if __name__ == "__main__":
    # Test decoder with pump.fun TradeEvent data
    # The old test data was for pAMM program, need new TradeEvent data for testing
    parser = PumpFunEventDecoder()
    new_data = "vdt/007mYe5Ch1LroXuZV8WHD/6ZgKnZzRGYnBEJGpVL416wKPMCf2vfHgAAAAAA1jOXlwkAAAABcYQFc5m8bV8rXe9QsVUUlcxzb542sPWzE1C4ETtTeMtb8TVoAAAAAMnS9UEJAAAAFSIkJE/gAgDJJtJFAgAAABWKEdi94QEASsL40N1cvJfjKJwZfLUGKlTz2Va5zm5RFfllZ6pcs+ZfAAAAAAAAABZLAAAAAAAAFwKQWogS8aNKX4qv+e2B5nvOQFZCad+Hug5i3dfr/oIFAAAAAAAAAPQDAAAAAAAA"
    res = parser.decode_log_data(new_data)
    print(res)