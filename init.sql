-- Initialize TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Drop existing tables if they exist (for clean rebuild)
DROP TABLE IF EXISTS swaps CASCADE;
DROP TABLE IF EXISTS tokens CASCADE;

-- Create tokens table for storing token metadata
CREATE TABLE tokens (
    mint_address TEXT PRIMARY KEY,
    name TEXT,
    symbol TEXT,
    decimals INTEGER NOT NULL DEFAULT 6,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create swaps table for storing pump.fun swap data
CREATE TABLE swaps (
    id SERIAL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    signature TEXT NOT NULL,
    mint_address TEXT NOT NULL REFERENCES tokens(mint_address),
    token_amount BIGINT NOT NULL,
    sol_amount BIGINT NOT NULL,
    price_per_token DECIMAL(20, 10) NOT NULL,
    swap_type TEXT NOT NULL CHECK (swap_type IN ('buy', 'sell')),
    user_wallet TEXT NOT NULL,
    -- Composite primary key including timestamp for TimescaleDB
    PRIMARY KEY (id, timestamp)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('swaps', 'timestamp', if_not_exists => TRUE);

-- Create indexes for better query performance
CREATE INDEX idx_swaps_mint_address ON swaps (mint_address);
CREATE INDEX idx_swaps_signature ON swaps (signature);
CREATE INDEX idx_swaps_user_wallet ON swaps (user_wallet);
CREATE INDEX idx_swaps_swap_type ON swaps (swap_type);
CREATE INDEX idx_swaps_mint_timestamp ON swaps (mint_address, timestamp);
CREATE INDEX idx_swaps_user_timestamp ON swaps (user_wallet, timestamp);
CREATE INDEX idx_swaps_type_timestamp ON swaps (swap_type, timestamp);

-- Create unique constraint on signature + timestamp for TimescaleDB
CREATE UNIQUE INDEX idx_swaps_signature_timestamp ON swaps (signature, timestamp);

-- Create indexes for tokens table
CREATE INDEX idx_tokens_symbol ON tokens (symbol);
CREATE INDEX idx_tokens_name ON tokens (name);

-- Create a view for recent swaps with token information
CREATE OR REPLACE VIEW recent_swaps AS
SELECT
    s.*,
    t.name as token_name,
    t.symbol as token_symbol,
    t.decimals as token_decimals,
    EXTRACT(EPOCH FROM (NOW() - s.timestamp)) as seconds_ago,
    (s.sol_amount::DECIMAL / 1000000000) as sol_display,
    (s.token_amount::DECIMAL / POWER(10, t.decimals)) as token_display
FROM swaps s
JOIN tokens t ON s.mint_address = t.mint_address
WHERE s.timestamp >= NOW() - INTERVAL '24 hours'
ORDER BY s.timestamp DESC;

-- Create a view for token statistics
CREATE OR REPLACE VIEW token_stats AS
SELECT
    s.mint_address,
    t.name as token_name,
    t.symbol as token_symbol,
    COUNT(*) as total_swaps,
    COUNT(*) FILTER (WHERE s.swap_type = 'buy') as buy_count,
    COUNT(*) FILTER (WHERE s.swap_type = 'sell') as sell_count,
    (SUM(s.sol_amount) FILTER (WHERE s.swap_type = 'buy'))::DECIMAL / 1000000000 as total_sol_bought,
    (SUM(s.sol_amount) FILTER (WHERE s.swap_type = 'sell'))::DECIMAL / 1000000000 as total_sol_sold,
    AVG(s.price_per_token) as avg_price,
    MAX(s.price_per_token) as max_price,
    MIN(s.price_per_token) as min_price,
    MAX(s.timestamp) as last_swap_time
FROM swaps s
JOIN tokens t ON s.mint_address = t.mint_address
GROUP BY s.mint_address, t.name, t.symbol;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO gemfinder;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO gemfinder;
