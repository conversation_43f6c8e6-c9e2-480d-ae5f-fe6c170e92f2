#!/usr/bin/env python3
"""
Test script to verify the pump.fun AMM decoder functionality.
"""

import base64
import struct
from app.transaction_decoder import PumpAMMEventDecoder

def test_decoder():
    """Test the event decoder with sample data."""
    decoder = PumpAMMEventDecoder()

    # Create a mock BuyEvent data structure
    # This simulates what we would receive from a real pump.fun transaction

    # BuyEvent discriminator (8 bytes) - correct from IDL
    discriminator = bytes([103, 244, 82, 31, 44, 245, 119, 119])

    # Mock event data (little-endian format) - BuyEvent structure from IDL
    timestamp = struct.pack('<q', 1700000000)  # i64
    base_amount_out = struct.pack('<Q', 1000000)  # u64 - 1M tokens
    max_quote_amount_in = struct.pack('<Q', 60000000)  # u64 - max amount willing to pay
    user_base_reserves = struct.pack('<Q', 5000000)  # u64
    user_quote_reserves = struct.pack('<Q', 100000000)  # u64
    pool_base_reserves = struct.pack('<Q', 1000000000000)  # u64
    pool_quote_reserves = struct.pack('<Q', 10000000000)  # u64
    quote_amount_in = struct.pack('<Q', ********)  # u64 - actual amount paid

    # Fee fields (6 u64s)
    lp_fee_basis_points = struct.pack('<Q', 30)  # 0.3%
    lp_fee = struct.pack('<Q', 15000)
    protocol_fee_basis_points = struct.pack('<Q', 10)  # 0.1%
    protocol_fee = struct.pack('<Q', 5000)
    quote_amount_in_with_lp_fee = struct.pack('<Q', ********)
    user_quote_amount_in = struct.pack('<Q', ********)

    # Mock pubkeys (32 bytes each) - 7 pubkeys
    pool_pubkey = b'A' * 32
    user_pubkey = b'B' * 32
    user_base_token_account = b'C' * 32
    user_quote_token_account = b'D' * 32
    protocol_fee_recipient = b'E' * 32
    protocol_fee_recipient_token_account = b'F' * 32
    coin_creator = b'G' * 32

    # Additional fee fields (2 u64s)
    coin_creator_fee_basis_points = struct.pack('<Q', 5)  # 0.05%
    coin_creator_fee = struct.pack('<Q', 2500)

    # Construct the full event data (16 u64s + 7 pubkeys + 2 u64s)
    event_data = (
        timestamp + base_amount_out + max_quote_amount_in +
        user_base_reserves + user_quote_reserves +
        pool_base_reserves + pool_quote_reserves +
        quote_amount_in + lp_fee_basis_points + lp_fee +
        protocol_fee_basis_points + protocol_fee +
        quote_amount_in_with_lp_fee + user_quote_amount_in +
        pool_pubkey + user_pubkey + user_base_token_account +
        user_quote_token_account + protocol_fee_recipient +
        protocol_fee_recipient_token_account + coin_creator +
        coin_creator_fee_basis_points + coin_creator_fee
    )

    # Full log data with discriminator
    full_data = discriminator + event_data

    # Encode as base64 (as it would come from Solana logs)
    log_data = base64.b64encode(full_data).decode('utf-8')

    print(f"Testing with mock log data: {log_data[:50]}...")

    # Decode the event
    result = decoder.decode_log_data(log_data)

    if result:
        print("✅ Successfully decoded BuyEvent:")
        print(f"  Event type: {result['event_type']}")
        print(f"  Timestamp: {result['timestamp']}")
        print(f"  Base amount out: {result['base_amount_out']:,} tokens")
        print(f"  Quote amount in: {result['quote_amount_in']:,} lamports")
        print(f"  Price per token: {result['price_per_token']:.8f} lamports")
        print(f"  Pool: {result['pool'][:16]}...")
        print(f"  User: {result['user'][:16]}...")
        print(f"  Max quote amount in: {result['max_quote_amount_in']:,} lamports")
    else:
        print("❌ Failed to decode event")

    return result

def test_sell_event():
    """Test decoding a sell event."""
    decoder = PumpAMMEventDecoder()

    # SellEvent discriminator - correct from IDL
    discriminator = bytes([62, 47, 55, 10, 165, 3, 220, 42])

    # Mock sell event data - SellEvent structure from IDL
    timestamp = struct.pack('<q', 1700000001)  # i64
    base_amount_in = struct.pack('<Q', 500000)  # u64 - 0.5M tokens
    min_quote_amount_out = struct.pack('<Q', 20000000)  # u64 - minimum expected
    user_base_reserves = struct.pack('<Q', 4500000)  # u64
    user_quote_reserves = struct.pack('<Q', 1********)  # u64
    pool_base_reserves = struct.pack('<Q', 1000000500000)  # u64
    pool_quote_reserves = struct.pack('<Q', 9976000000)  # u64
    quote_amount_out = struct.pack('<Q', ********)  # u64 - actual amount received

    # Fee fields (6 u64s)
    lp_fee_basis_points = struct.pack('<Q', 30)  # 0.3%
    lp_fee = struct.pack('<Q', 7200)
    protocol_fee_basis_points = struct.pack('<Q', 10)  # 0.1%
    protocol_fee = struct.pack('<Q', 2400)
    quote_amount_out_without_lp_fee = struct.pack('<Q', ********)
    user_quote_amount_out = struct.pack('<Q', ********)

    # Mock pubkeys (32 bytes each) - 7 pubkeys
    pool_pubkey = b'E' * 32
    user_pubkey = b'F' * 32
    user_base_token_account = b'G' * 32
    user_quote_token_account = b'H' * 32
    protocol_fee_recipient = b'I' * 32
    protocol_fee_recipient_token_account = b'J' * 32
    coin_creator = b'K' * 32

    # Additional fee fields (2 u64s)
    coin_creator_fee_basis_points = struct.pack('<Q', 5)  # 0.05%
    coin_creator_fee = struct.pack('<Q', 1200)

    # Construct the full event data (16 u64s + 7 pubkeys + 2 u64s)
    event_data = (
        timestamp + base_amount_in + min_quote_amount_out +
        user_base_reserves + user_quote_reserves +
        pool_base_reserves + pool_quote_reserves +
        quote_amount_out + lp_fee_basis_points + lp_fee +
        protocol_fee_basis_points + protocol_fee +
        quote_amount_out_without_lp_fee + user_quote_amount_out +
        pool_pubkey + user_pubkey + user_base_token_account +
        user_quote_token_account + protocol_fee_recipient +
        protocol_fee_recipient_token_account + coin_creator +
        coin_creator_fee_basis_points + coin_creator_fee
    )

    full_data = discriminator + event_data
    log_data = base64.b64encode(full_data).decode('utf-8')

    print(f"\nTesting sell event with mock log data: {log_data[:50]}...")

    result = decoder.decode_log_data(log_data)

    if result:
        print("✅ Successfully decoded SellEvent:")
        print(f"  Event type: {result['event_type']}")
        print(f"  Timestamp: {result['timestamp']}")
        print(f"  Base amount in: {result['base_amount_in']:,} tokens")
        print(f"  Quote amount out: {result['quote_amount_out']:,} lamports")
        print(f"  Min quote amount out: {result['min_quote_amount_out']:,} lamports")
        print(f"  Price per token: {result['price_per_token']:.8f} lamports")
    else:
        print("❌ Failed to decode sell event")

    return result

if __name__ == "__main__":
    print("🧪 Testing pump.fun AMM Event Decoder")
    print("=" * 50)

    # Test buy event
    buy_result = test_decoder()

    # Test sell event
    sell_result = test_sell_event()

    print("\n" + "=" * 50)
    if buy_result and sell_result:
        print("✅ All tests passed! The decoder is working correctly.")
        print("\n📊 Summary:")
        print(f"  Buy: {buy_result['base_amount_out']:,} tokens for {buy_result['quote_amount_in']:,} lamports")
        print(f"  Sell: {sell_result['base_amount_in']:,} tokens for {sell_result['quote_amount_out']:,} lamports")
    else:
        print("❌ Some tests failed. Check the decoder implementation.")
