#!/usr/bin/env python3
"""Test script to verify the new optimized models work correctly."""

import os
import sys
from datetime import datetime, timezone
from decimal import Decimal

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import SessionLocal
from app.models import Token, Swap, SwapCreate, TokenCreate

def test_models():
    """Test the new optimized models."""
    print("Testing new optimized models...")
    
    db = SessionLocal()
    try:
        # Create a test token
        test_token = Token(
            mint_address="So********************************111111112",  # Wrapped SOL
            name="Wrapped SOL",
            symbol="WSOL",
            decimals=9
        )
        db.add(test_token)
        db.flush()
        
        print(f"✅ Created token: {test_token.symbol} ({test_token.mint_address})")
        
        # Create a test swap
        test_swap = Swap(
            signature="test_signature_123456789",
            mint_address=test_token.mint_address,
            token_amount=1000000000,  # 1 token (with 9 decimals)
            sol_amount=100000000,     # 0.1 SOL (in lamports)
            price_per_token=0.0001,   # Price per token in SOL
            swap_type="buy",
            user_wallet="********************************"
        )
        db.add(test_swap)
        db.commit()
        
        print(f"✅ Created swap: {test_swap.swap_type} {test_swap.token_amount} tokens for {test_swap.sol_amount} lamports")
        
        # Test querying with joins
        swap_with_token = db.query(
            Swap.id,
            Swap.signature,
            Swap.token_amount,
            Swap.sol_amount,
            Swap.swap_type,
            Token.name.label('token_name'),
            Token.symbol.label('token_symbol'),
            Token.decimals.label('token_decimals')
        ).join(Token, Swap.mint_address == Token.mint_address).first()
        
        if swap_with_token:
            sol_display = swap_with_token.sol_amount / 1_000_000_000
            token_display = swap_with_token.token_amount / (10 ** swap_with_token.token_decimals)
            
            print(f"✅ Query successful:")
            print(f"   Token: {swap_with_token.token_symbol} ({swap_with_token.token_name})")
            print(f"   SOL Amount: {sol_display:.9f} SOL")
            print(f"   Token Amount: {token_display:.6f} {swap_with_token.token_symbol}")
            print(f"   Type: {swap_with_token.swap_type}")
        
        # Test the SwapCreate model
        swap_create_data = SwapCreate(
            signature="test_signature_987654321",
            mint_address=test_token.mint_address,
            token_amount=2000000000,
            sol_amount=200000000,
            price_per_token=0.0001,
            swap_type="sell",
            user_wallet="22222222222222222222222222222222"
        )
        
        new_swap = Swap(**swap_create_data.model_dump())
        db.add(new_swap)
        db.commit()
        
        print(f"✅ Created swap via SwapCreate model: {new_swap.swap_type}")
        
        # Count total swaps and tokens
        total_swaps = db.query(Swap).count()
        total_tokens = db.query(Token).count()
        
        print(f"✅ Database stats: {total_swaps} swaps, {total_tokens} tokens")
        
        print("\n🎉 All tests passed! The new optimized models are working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    test_models()
