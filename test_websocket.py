#!/usr/bin/env python3
"""
Test script to verify the Solana SDK websocket connection works.
"""

import asyncio
import logging
from solana.rpc.websocket_api import connect

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """Test basic websocket connection to Solana."""
    try:
        ws_url = "wss://api.mainnet-beta.solana.com"
        logger.info(f"Testing connection to {ws_url}")
        
        async with connect(ws_url) as websocket:
            logger.info("✅ Successfully connected to Solana WebSocket!")
            
            # Test a simple subscription
            logger.info("Testing slot subscription...")
            subscription_id = await websocket.slot_subscribe()
            logger.info(f"✅ Slot subscription successful with ID: {subscription_id}")
            
            # Listen for a few messages
            message_count = 0
            async for msg_list in websocket:
                message_count += len(msg_list)
                logger.info(f"Received {len(msg_list)} message(s) (total: {message_count})")
                
                if message_count >= 3:  # Stop after receiving a few messages
                    logger.info("✅ Successfully received messages from Solana WebSocket!")
                    break
                    
    except Exception as e:
        logger.error(f"❌ WebSocket connection failed: {e}")
        raise

if __name__ == "__main__":
    print("🧪 Testing Solana SDK WebSocket Connection")
    print("=" * 50)
    
    try:
        asyncio.run(test_websocket_connection())
        print("=" * 50)
        print("✅ WebSocket test completed successfully!")
    except Exception as e:
        print("=" * 50)
        print(f"❌ WebSocket test failed: {e}")
